// Copyright Epic Games, Inc. All Rights Reserved.

/*=============================================================================
 OpenColorIOShader.usf: Simple shader to apply OCIO color tranform. Transform operation shader code is generated by OCIO
 =============================================================================*/

#include "/Engine/Public/Platform.ush"

/* Declaration of all variables
=============================================================================*/
Texture2D		InputTexture;
SamplerState	InputTextureSampler;
float			Gamma;
uint			TransformAlpha;

/*=============================================================================
 * OCIO generated start
 *=============================================================================*/

#include "/Engine/Generated/OpenColorIOTransformShader.ush"

/*=============================================================================
 * OCIO generated end
 *=============================================================================*/


/* Helpers
=============================================================================*/

float SafeRcp(float x)
{
	return x > 0.0 ? rcp(x) : 0.0;
}

float4 InvertAlpha(float4 InColor)
{
	return float4(InColor.xyz, 1.0 - InColor.w);
}

float4 Unpremultiply(float4 InColor)
{
	return float4(InColor.xyz * SafeRcp(InColor.w), InColor.w);
}

float4 Premultiply(float4 InColor)
{
	return float4(InColor.xyz * InColor.w, InColor.w);
}

// Note: For our (zero-tolerance) tests to succeed against a no-op, precise is required with the pow function.
float3 DisplayGammaCorrection(precise float3 InColor, precise float InGamma)
{
	return sign(InColor) * pow(abs(InColor), InGamma);
}

/* Pixel shader
=============================================================================*/

void MainPS(in noperspective float4 InUVAndScreenPos : TEXCOORD0,
	out float4 OutColor : SV_Target0)
{
	float4 InputColor = InputTexture.Sample(InputTextureSampler, InUVAndScreenPos.xy);
	InputColor.xyz = DisplayGammaCorrection(InputColor.xyz, Gamma);

	if(TransformAlpha > 0)
	{
		if(TransformAlpha == 2 /*InvertUnpremultiply*/)
		{
			InputColor = InvertAlpha(InputColor);
		}

		InputColor = Unpremultiply(InputColor);
	}

	// Apply main transform
	OutColor = float4(OCIOConvert(InputColor).xyz, InputColor.w);

	if(TransformAlpha > 0)
	{
		OutColor = Premultiply(OutColor);
		
		if(TransformAlpha == 2 /*InvertUnpremultiply*/)
		{
			OutColor = InvertAlpha(OutColor);
		}
	}
}