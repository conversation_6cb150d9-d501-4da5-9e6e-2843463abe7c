#usda 1.0
(
    "WARNING: THIS FILE IS GENERATED BY usdGenSchema.  DO NOT EDIT."
)

class SkelRoot "SkelRoot" (
    doc = '''Boundable prim type used to identify a scope beneath which
    skeletally-posed primitives are defined.

    A SkelRoot must be defined at or above a skinned primitive for any skinning
    behaviors in UsdSkel.

    See the extended "Skel Root Schema" documentation for
    more information.'''
)
{
    float3[] extent (
        doc = """Extent is a three dimensional range measuring the geometric
        extent of the authored gprim in its own local space (i.e. its own
        transform not applied), without accounting for any shader-induced
        displacement. If __any__ extent value has been authored for a given 
        Boundable, then it should be authored at every timeSample at which 
        geometry-affecting properties are authored, to ensure correct 
        evaluation via ComputeExtent(). If __no__ extent value has been 
        authored, then ComputeExtent() will call the Boundable's registered 
        ComputeExtentFunction(), which may be expensive, which is why we 
        strongly encourage proper authoring of extent.
        \\sa ComputeExtent()
        \\sa \\ref UsdGeom_Boundable_Extent.
        
        An authored extent on a prim which has children is expected to include
        the extent of all children, as they will be pruned from BBox computation
        during traversal."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class Skeleton "Skeleton" (
    doc = '''Describes a skeleton. 

    See the extended "Skeleton Schema" documentation for
    more information.
    '''
)
{
    uniform matrix4d[] bindTransforms (
        doc = """Specifies the bind-pose transforms of each joint in
        **world space**, in the ordering imposed by *joints*."""
    )
    float3[] extent (
        doc = """Extent is a three dimensional range measuring the geometric
        extent of the authored gprim in its own local space (i.e. its own
        transform not applied), without accounting for any shader-induced
        displacement. If __any__ extent value has been authored for a given 
        Boundable, then it should be authored at every timeSample at which 
        geometry-affecting properties are authored, to ensure correct 
        evaluation via ComputeExtent(). If __no__ extent value has been 
        authored, then ComputeExtent() will call the Boundable's registered 
        ComputeExtentFunction(), which may be expensive, which is why we 
        strongly encourage proper authoring of extent.
        \\sa ComputeExtent()
        \\sa \\ref UsdGeom_Boundable_Extent.
        
        An authored extent on a prim which has children is expected to include
        the extent of all children, as they will be pruned from BBox computation
        during traversal."""
    )
    uniform token[] jointNames (
        doc = """If authored, provides a unique name per joint. This may be
        optionally set to provide better names when translating to DCC apps 
        that require unique joint names."""
    )
    uniform token[] joints (
        doc = """An array of path tokens identifying the set of joints that make
        up the skeleton, and their order. Each token in the array must be valid
        when parsed as an SdfPath. The parent-child relationships of the
        corresponding paths determine the parent-child relationships of each
        joint. It is not required that the name at the end of each path be
        unique, but rather only that the paths themselves be unique."""
    )
    rel proxyPrim (
        doc = '''The proxyPrim relationship allows us to link a
        prim whose purpose is "render" to its (single target)
        purpose="proxy" prim.  This is entirely optional, but can be
        useful in several scenarios:
        
        - In a pipeline that does pruning (for complexity management)
        by deactivating prims composed from asset references, when we
        deactivate a purpose="render" prim, we will be able to discover
        and additionally deactivate its associated purpose="proxy" prim,
        so that preview renders reflect the pruning accurately.
        
        - DCC importers may be able to make more aggressive optimizations
        for interactive processing and display if they can discover the proxy
        for a given render prim.
        
        - With a little more work, a Hydra-based application will be able
        to map a picked proxy prim back to its render geometry for selection.

        \\note It is only valid to author the proxyPrim relationship on
        prims whose purpose is "render".'''
    )
    uniform token purpose = "default" (
        allowedTokens = ["default", "render", "proxy", "guide"]
        doc = """Purpose is a classification of geometry into categories that 
        can each be independently included or excluded from traversals of prims 
        on a stage, such as rendering or bounding-box computation traversals.

        See for more detail about how 
        purpose is computed and used."""
    )
    uniform matrix4d[] restTransforms (
        doc = """Specifies the rest-pose transforms of each joint in
        **local space**, in the ordering imposed by *joints*. This provides
        fallback values for joint transforms when a Skeleton either has no
        bound animation source, or when that animation source only contains
        animation for a subset of a Skeleton's joints."""
    )
    token visibility = "inherited" (
        allowedTokens = ["inherited", "invisible"]
        doc = '''Visibility is meant to be the simplest form of "pruning" 
        visibility that is supported by most DCC apps.  Visibility is 
        animatable, allowing a sub-tree of geometry to be present for some 
        segment of a shot, and absent from others; unlike the action of 
        deactivating geometry prims, invisible geometry is still 
        available for inspection, for positioning, for defining volumes, etc.'''
    )
    uniform token[] xformOpOrder (
        doc = """Encodes the sequence of transformation operations in the
        order in which they should be pushed onto a transform stack while
        visiting a UsdStage's prims in a graph traversal that will effect
        the desired positioning for this prim and its descendant prims.
        
        You should rarely, if ever, need to manipulate this attribute directly.
        It is managed by the AddXformOp(), SetResetXformStack(), and
        SetXformOpOrder(), and consulted by GetOrderedXformOps() and
        GetLocalTransformation()."""
    )
}

class SkelAnimation "SkelAnimation" (
    doc = '''Describes a skel animation, where joint animation is stored in a
    vectorized form.

    See the extended "Skel Animation"
    documentation for more information.
    '''
)
{
    uniform token[] blendShapes (
        doc = """Array of tokens identifying which blend shapes this
         animation's data applies to. The tokens for blendShapes correspond to
         the tokens set in the *skel:blendShapes* binding property of the
         UsdSkelBindingAPI. Note that blendShapes does not accept time-sampled
         values."""
    )
    float[] blendShapeWeights (
        doc = """Array of weight values for each blend shape. Each weight value
        is associated with the corresponding blend shape identified within the
        *blendShapes* token array, and therefore must have the same length as
        *blendShapes."""
    )
    uniform token[] joints (
        doc = """Array of tokens identifying which joints this animation's
        data applies to. The tokens for joints correspond to the tokens of
        Skeleton primitives. The order of the joints as listed here may
        vary from the order of joints on the Skeleton itself."""
    )
    quatf[] rotations (
        doc = """Joint-local unit quaternion rotations of all affected joints, 
        in 32-bit precision. Array length should match the size of the 
        *joints* attribute."""
    )
    half3[] scales (
        doc = """Joint-local scales of all affected joints, in
        16 bit precision. Array length should match the size of the *joints* 
        attribute."""
    )
    float3[] translations (
        doc = """Joint-local translations of all affected joints. Array length 
        should match the size of the *joints* attribute."""
    )
}

class "SkelBindingAPI" (
    doc = '''Provides API for authoring and extracting all the skinning-related
    data that lives in the "geometry hierarchy" of prims and models that want
    to be skeletally deformed.

    See the extended "UsdSkelBindingAPI schema"
    documentation for more about bindings and how they apply in a scene graph.
    '''
)
{
    matrix4d primvars:skel:geomBindTransform (
        doc = '''Encodes the bind-time world space transforms of the prim.
        If the transform is identical for a group of gprims that share a common
        ancestor, the transform may be authored on the ancestor, to "inherit"
        down to all the leaf gprims. If this transform is unset, an identity
        transform is used instead.'''
    )
    int[] primvars:skel:jointIndices (
        doc = """Indices into the *joints* attribute of the closest
        (in namespace) bound Skeleton that affect each point of a PointBased
        gprim. The primvar can have either *constant* or *vertex* interpolation.
        This primvar's *elementSize* will determine how many joint influences
        apply to each point. Indices must point be valid. Null influences should
        be defined by setting values in jointWeights to zero.
        See UsdGeomPrimvar for more information on interpolation and
        elementSize."""
    )
    float[] primvars:skel:jointWeights (
        doc = """Weights for the joints that affect each point of a PointBased
        gprim. The primvar can have either *constant* or *vertex* interpolation.
        This primvar's *elementSize* will determine how many joints influences
        apply to each point. The length, interpolation, and elementSize of
        *jointWeights* must match that of *jointIndices*. See UsdGeomPrimvar
        for more information on interpolation and elementSize."""
    )
    uniform token primvars:skel:skinningMethod = "classicLinear" (
        allowedTokens = ["classicLinear", "dualQuaternion"]
        doc = "The skinningMethod specifies the skinning method for the prim."
    )
    rel skel:animationSource (
        doc = """Animation source to be bound to Skeleton primitives at or
        beneath the location at which this property is defined.
        """
    )
    uniform token[] skel:blendShapes (
        doc = """An array of tokens defining the order onto which blend shape
        weights from an animation source map onto the *skel:blendShapeTargets*
        rel of a binding site. If authored, the number of elements must be equal
        to the number of targets in the _blendShapeTargets_ rel. This property
        is not inherited hierarchically, and is expected to be authored directly
        on the skinnable primitive to which the blend shapes apply."""
    )
    rel skel:blendShapeTargets (
        doc = """Ordered list of all target blend shapes. This property is not
        inherited hierarchically, and is expected to be authored directly on
        the skinnable primitive to which the the blend shapes apply."""
    )
    uniform token[] skel:joints (
        doc = """An (optional) array of tokens defining the list of
        joints to which jointIndices apply. If not defined, jointIndices applies
        to the ordered list of joints defined in the bound Skeleton's *joints*
        attribute. If undefined on a primitive, the primitive inherits the 
        value of the nearest ancestor prim, if any."""
    )
    rel skel:skeleton (
        doc = """Skeleton to be bound to this prim and its descendents that
        possess a mapping and weighting to the joints of the identified
        Skeleton."""
    )
}

class BlendShape "BlendShape" (
    doc = '''Describes a target blend shape, possibly containing inbetween
      shapes.

      See the extended "Blend Shape Schema
      documentation for information.
      '''
)
{
    uniform vector3f[] normalOffsets (
        doc = """**Required property**. Normal offsets which, when added to the
        base pose, provides the normals of the target shape."""
    )
    uniform vector3f[] offsets (
        doc = """**Required property**. Position offsets which, when added to the
        base pose, provides the target shape."""
    )
    uniform int[] pointIndices (
        doc = """**Optional property**. Indices into the original mesh that
        correspond to the values in *offsets* and of any inbetween shapes. If
        authored, the number of elements must be equal to the number of elements
        in the *offsets* array."""
    )
}

