-- glslfx version 0.1

//
// Copyright 2018 Pixar
//
// Licensed under the terms set forth in the LICENSE.txt file available at
// https://openusd.org/license.
//

-- configuration
{
    "techniques": {
        "default": {
            "ColorCorrectionVertex": {
                "source": [ "ColorCorrection.Vertex" ]
            },
            "ColorCorrectionFragment": {
                "source": [ "ColorCorrection.Fragment" ]
            }
        }
    }
}

-- glsl ColorCorrection.Vertex

void main(void)
{
    gl_Position = position;
    uvOut = uvIn;
}

-- glsl ColorCorrection.Fragment

// Similar to D3DX_DXGIFormatConvert.inl, but branchless
// https://www.shadertoy.com/view/wds3zM
vec3 FloatToSRGB(vec3 val)
{
    val = mix((val * 12.92),
              (1.055 * pow(val, vec3(1.0/2.4)) - 0.055),
              step(0.0031308, val));
    return val;
}

void main(void)
{
    vec2 fragCoord = uvOut * screenSize;
    vec4 inCol = HgiTexelFetch_colorIn(ivec2(fragCoord));

    #if defined(GLSLFX_USE_OCIO)
        inCol = OCIO_DISPLAY_FUNC(inCol);
    #else
        // Only color, not alpha is gamma corrected!
        inCol.rgb = FloatToSRGB(inCol.rgb);
    #endif

    hd_FragColor = inCol;
}
