# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "unrealBendingWingAPI": {
                        "alias": {
                            "UsdSchemaBase": "BendingWingAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealChaosFabricAPI": {
                        "alias": {
                            "UsdSchemaBase": "ChaosFabricAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Mesh"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealChaosSolverPropertiesAPI": {
                        "alias": {
                            "UsdSchemaBase": "ChaosSolverPropertiesAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealCloFabricAPI": {
                        "alias": {
                            "UsdSchemaBase": "CloFabricAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Mesh"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealCloSolverPropertiesAPI": {
                        "alias": {
                            "UsdSchemaBase": "CloSolverPropertiesAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealClothRootAPI": {
                        "alias": {
                            "UsdSchemaBase": "ClothRootAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Scope"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealClothSolverAPI": {
                        "alias": {
                            "UsdSchemaBase": "ClothSolverAPI"
                        }, 
                        "apiSchemaAllowedInstanceNames": [
                            "chaos", 
                            "clo"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "multipleApplyAPI"
                    }, 
                    "unrealPinAPI": {
                        "alias": {
                            "UsdSchemaBase": "PinAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealRenderPatternAPI": {
                        "alias": {
                            "UsdSchemaBase": "RenderPatternAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealSewingAPI": {
                        "alias": {
                            "UsdSchemaBase": "SewingAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealSimMeshDataAPI": {
                        "alias": {
                            "UsdSchemaBase": "SimMeshDataAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Mesh"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealSimPatternAPI": {
                        "alias": {
                            "UsdSchemaBase": "SimPatternAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealSpringAPI": {
                        "alias": {
                            "UsdSchemaBase": "SpringAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealTackSpringAPI": {
                        "alias": {
                            "UsdSchemaBase": "TackSpringAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "GeomSubset"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "Name": "ueCloth", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "resource"
        }
    ]
}
