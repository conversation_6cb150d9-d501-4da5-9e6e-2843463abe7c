<?xml version="1.0" encoding="utf-8"?>
<!--OpenXR plugin additions-->
<root xmlns:android="http://schemas.android.com/apk/res/android">
	<!-- init section is always evaluated once per architecture -->
	<init>
		<log text="OpenXRLoader"/>
		<setBool result="bCpuArchSupported" value="false"/>
		<isArch arch="arm64-v8a">
			<setBool result="bCpuArchSupported" value="true"/>
		</isArch>
		<isArch arch="armeabi-v7a">
			<setBool result="bCpuArchSupported" value="true"/>
		</isArch>
		<isArch arch="x86">
			<setBool result="bCpuArchSupported" value="true"/>
		</isArch>
		<isArch arch="x86_64">
			<setBool result="bCpuArchSupported" value="true"/>
		</isArch>
	</init>

	<!-- optional updates applied to AndroidManifest.xml
	<androidManifestUpdates>
	</androidManifestUpdates> -->

	<!-- optional additions to the GameActivity class in GameActivity.java
	<gameActivityClassAdditions>
	</gameActivityClassAdditions> -->

	<!-- optional additions to GameActivity onCreate metadata reading in GameActivity.java
	<gameActivityReadMetadataAdditions>
	</gameActivityReadMetadataAdditions> -->


	<!-- optional files or directories to copy to Intermediate/Android/APK -->
	<resourceCopies>
		<if condition="bCpuArchSupported">
			<true>
				<log text="Copying libopenxr_loader.so"/>
				<copyFile src="$S(EngineDir)/Binaries/ThirdParty/OpenXR/Android/$S(Architecture)/libopenxr_loader.so"
								dst="$S(BuildDir)/libs/$S(Architecture)/libopenxr_loader.so" />
			</true>
		</if>
	</resourceCopies>


	<!-- optional libraries to load in GameActivity.java before libUnreal.so -->
	<soLoadLibrary>
		<!-- need this if plugin enabled and supported architecture -->
		<if condition="bCpuArchSupported">
			<true>
				<loadLibrary name="openxr_loader" failmsg="openxr_loader library not loaded and may be required for OpenXR." />
			</true>
		</if>
	</soLoadLibrary>
</root>
