<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name> astc-encoder 4.2.0</Name>
    <EndUserGroup>Git</EndUserGroup>
  <Location>//UE5/Main/Engine/Source/ThirdParty/astcenc/
  </Location>
  <Function> Encoding textures into ASTC format
  Will be used by the Unreal Editor to compress textures into ASTC format which is commonly used by iOS and Android platforms.
  </Function>
  <Eula>https://github.com/ARM-software/astc-encoder/blob/main/LICENSE.txt</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>

