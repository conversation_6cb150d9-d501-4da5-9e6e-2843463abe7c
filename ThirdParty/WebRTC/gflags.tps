<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>gflags</Name>
  <Location>/Engine/Source/ThirdParty/WebRTC/sdk_trunk*/include/third_party/gflags/</Location>
  <Date>2016-09-07T13:31:02.7287226-04:00</Date>
  <Function>Allows processing of commandline flags within WebRTC</Function>
  <Justification />
  <Eula>https://chromium.googlesource.com/external/webrtc/+/branch-heads/43/third_party/gflags/LICENSE</Eula>
  <RedistributeTo>
    <EndUserGroup>Licensees</EndUserGroup>
    <EndUserGroup>Git</EndUserGroup>
    <EndUserGroup>P4</EndUserGroup>
  </RedistributeTo>
  <LicenseFolder>/Engine/Source/ThirdParty/Licenses/gflags_license.txt</LicenseFolder>
</TpsData>