{"FileVersion": 1, "Version": 1, "VersionName": "1.0", "FriendlyName": "Cascade To Niagara Converter", "Description": "Add support for scriptable conversion of Cascade Systems to Niagara Systems.", "Category": "FX", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Modules": [{"Name": "CascadeToNiagaraConverter", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}], "Plugins": [{"Name": "Niagara", "Enabled": true}, {"Name": "PythonScriptPlugin", "Enabled": true}, {"Name": "EditorScriptingUtilities", "Enabled": true}]}