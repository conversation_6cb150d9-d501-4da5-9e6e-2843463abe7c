{"FileVersion": 3, "Version": 1, "VersionName": "1.0", "FriendlyName": "Remote Control API", "Description": "A suite of tools for controlling the Unreal Engine, both in Editor or at Runtime via a webserver. This allows users to control Unreal Engine remotely through HTTP or WebSockets requests. This functionality allows developers to control Unreal through 3rd party applications and web services.", "Category": "Messaging", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "CanContainContent": true, "IsBetaVersion": true, "Installed": false, "Plugins": [{"Name": "WebSocketNetworking", "Enabled": true}, {"Name": "RemoteControlInterception", "Enabled": true}, {"Name": "ConcertSyncClient", "Enabled": true}], "Modules": [{"Name": "RemoteControl", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RemoteControlLogic", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "WebRemoteControl", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["<PERSON>", "Win64", "Linux"]}, {"Name": "RemoteControlCommon", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RemoteControlUI", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RemoteControlProtocol", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RemoteControlProtocolWidgets", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>"}, {"Name": "RemoteControlMultiUser", "Type": "UncookedOnly", "LoadingPhase": "PostEngineInit"}]}